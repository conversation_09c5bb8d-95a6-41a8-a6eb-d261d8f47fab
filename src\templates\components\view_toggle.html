<!-- 视图切换组件 -->

<style>
/* 视图切换容器 */
.main_graph_table_view_toggle {
    position: fixed;
    left: 50%;
    bottom: 18px;
    transform: translateX(-50%);
    z-index: 2000;
    display: flex;
    gap: 10px;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 20px;
    border-radius: 50px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 切换按钮样式 */
.main_graph_table_toggle_btn {
    width: 120px;
    height: 50px;
    border-radius: 25px;
    background: linear-gradient(135deg, #1e5799, #2989d8);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.main_graph_table_toggle_btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.5s ease;
}

.main_graph_table_toggle_btn:hover:before {
    left: 100%;
}

.main_graph_table_toggle_btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 123, 255, 0.5);
}

.main_graph_table_toggle_btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 5px rgba(0, 123, 255, 0.3);
}

/* 响应式样式 */
@media (max-width: 992px) {
    .main_graph_table_view_toggle {
        flex-direction: column;
        left: auto;
        right: 20px;
        bottom: 20px;
        transform: none;
    }

    .main_graph_table_toggle_btn {
        width: 50px;
        border-radius: 50%;
    }
}
</style>

<!-- 视图切换按钮 -->
<div class="main_graph_table_view_toggle">
    <button id="main_graph_table_toggle_graph_btn" class="main_graph_table_toggle_btn" title="图示视图">
        <i class="fas fa-project-diagram"></i> 图示视图
    </button>
    <button id="main_graph_table_toggle_table_btn" class="main_graph_table_toggle_btn" title="表格视图">
        <i class="fas fa-table"></i> 表格视图
    </button>
</div>

<script>
// 视图切换相关JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 获取切换按钮
    const toggleGraphBtn = document.getElementById('main_graph_table_toggle_graph_btn');
    const toggleTableBtn = document.getElementById('main_graph_table_toggle_table_btn');

    // 绑定切换事件
    if (toggleGraphBtn) {
        toggleGraphBtn.addEventListener('click', () => toggleView('graph'));
    }
    if (toggleTableBtn) {
        toggleTableBtn.addEventListener('click', () => toggleView('table'));
    }

    // 延迟执行默认视图设置，确保所有组件都已加载
    setTimeout(() => {
        toggleView('graph');
    }, 100);
});

// 切换视图函数
function toggleView(viewType) {
    const tableContainer = document.querySelector('.table-custom-container');
    const graphContainer = document.getElementById('main_graph_table_container');
    const paginationContainer = document.getElementById('pagination');
    const panelToggle = document.getElementById('panelToggle');
    const leftPanel = document.getElementById('leftPanel');
    const centerButtons = document.querySelector('.center-buttons');
    const columnBubblesContainer = document.querySelector('.column-bubbles-container');
    const tableInfoModule = document.getElementById('tableInfoModule');
    const dynamicTableTitle = document.getElementById('dynamicTableTitle');
    const toggleGraphBtn = document.getElementById('main_graph_table_toggle_graph_btn');
    const toggleTableBtn = document.getElementById('main_graph_table_toggle_table_btn');

    if (viewType === 'table') {
        // 切换到表格视图
        if (graphContainer) {
            graphContainer.classList.add('hidden');
            graphContainer.style.display = 'none';
        }
        if (tableContainer) {
            tableContainer.classList.remove('hidden');
            tableContainer.style.display = '';
        }
        if (toggleTableBtn) toggleTableBtn.style.background = 'linear-gradient(135deg, #3498db, #2980b9)';
        if (toggleGraphBtn) toggleGraphBtn.style.background = 'linear-gradient(135deg, #1e5799, #2989d8)';

        // 显示表格视图相关元素
        if (panelToggle) panelToggle.style.display = '';
        if (leftPanel) leftPanel.style.display = '';
        if (centerButtons) centerButtons.style.display = 'flex';
        if (columnBubblesContainer) columnBubblesContainer.style.display = 'flex';
        if (tableInfoModule) tableInfoModule.style.display = 'flex';
        if (dynamicTableTitle) dynamicTableTitle.style.display = 'block';
        if (paginationContainer) paginationContainer.style.display = '';

        // 恢复页面主体滚动条，隐藏图示视图滚动条
        document.body.classList.remove('graph-view-active');
        document.body.classList.add('table-view-active');

        // 移除图示视图容器的激活类
        if (graphContainer) {
            graphContainer.classList.remove('graph-view-active');
        }
    } else {
        // 切换到图示视图
        if (graphContainer) {
            graphContainer.classList.remove('hidden');
            graphContainer.style.display = '';
        }
        if (tableContainer) {
            tableContainer.classList.add('hidden');
            tableContainer.style.display = 'none';
        }
        if (toggleGraphBtn) toggleGraphBtn.style.background = 'linear-gradient(135deg, #3498db, #2980b9)';
        if (toggleTableBtn) toggleTableBtn.style.background = 'linear-gradient(135deg, #1e5799, #2989d8)';

        // 隐藏表格视图相关元素
        if (panelToggle) panelToggle.style.display = 'none';
        if (leftPanel) leftPanel.style.display = 'none';
        if (centerButtons) centerButtons.style.display = 'none';
        if (columnBubblesContainer) columnBubblesContainer.style.display = 'none';
        if (tableInfoModule) tableInfoModule.style.display = 'none';
        if (dynamicTableTitle) dynamicTableTitle.style.display = 'none';
        if (paginationContainer) paginationContainer.style.display = 'none';

        // 关闭右侧搜索面板（如果存在）
        const sideSearchPanel = document.getElementById('sideSearchPanel');
        if (sideSearchPanel && sideSearchPanel.classList.contains('active')) {
            sideSearchPanel.classList.remove('active');
        }

        // 隐藏页面主体滚动条，只保留图示视图内部滚动条
        document.body.classList.remove('table-view-active');
        document.body.classList.add('graph-view-active');

        // 为图示视图容器添加激活类
        if (graphContainer) {
            graphContainer.classList.add('graph-view-active');
        }
    }
}

// 添加滑动手势支持
let touchStartX = 0;
let touchEndX = 0;

document.addEventListener('touchstart', function(e) {
    touchStartX = e.changedTouches[0].screenX;
}, { passive: true });

document.addEventListener('touchend', function(e) {
    touchEndX = e.changedTouches[0].screenX;
    handleSwipe();
}, { passive: true });

function handleSwipe() {
    const minSwipeDistance = 50;
    const swipeDistance = touchEndX - touchStartX;

    if (Math.abs(swipeDistance) < minSwipeDistance) return;

    if (swipeDistance > 0) {
        // 右滑，切换到表格视图
        toggleView('table');
    } else {
        // 左滑，切换到图示视图
        toggleView('graph');
    }
}

// 页面加载时初始化滚动条状态
document.addEventListener('DOMContentLoaded', function() {
    const graphContainer = document.getElementById('main_graph_table_container');
    const tableContainer = document.getElementById('table_container');

    // 检查当前显示的是哪个视图
    if (graphContainer && !graphContainer.classList.contains('hidden')) {
        // 图示视图是显示的
        document.body.classList.add('graph-view-active');
        document.body.classList.remove('table-view-active');
        graphContainer.classList.add('graph-view-active');
    } else if (tableContainer && !tableContainer.classList.contains('hidden')) {
        // 表格视图是显示的
        document.body.classList.add('table-view-active');
        document.body.classList.remove('graph-view-active');
        if (graphContainer) {
            graphContainer.classList.remove('graph-view-active');
        }
    }
});
</script>
