<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滚动条测试</title>
    <style>
        /* 基础样式 */
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            overflow-x: hidden;
            width: 100vw;
            min-width: 1100px;
        }

        /* 当图示视图显示时，隐藏页面主体的滚动条 */
        body.graph-view-active {
            overflow: hidden !important;
            height: 100vh !important;
        }

        /* 当图示视图隐藏时，恢复页面主体的滚动条 */
        body.table-view-active {
            overflow-x: hidden !important;
            overflow-y: auto !important;
            height: auto !important;
        }

        /* 图示视图容器 */
        .main_graph_table_container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background-color: rgba(230, 247, 255, 0.95);
            z-index: 1000;
            padding: 20px;
            overflow-y: auto;
            overflow-x: hidden;
            display: block;
            scrollbar-width: thin;
            scrollbar-color: #2989d8 #f0f0f0;
        }

        .main_graph_table_container.hidden {
            display: none !important;
        }

        /* 确保图示视图容器在激活时占满整个视口 */
        .main_graph_table_container.graph-view-active {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1000;
        }

        .main_graph_table_container::-webkit-scrollbar {
            width: 10px;
        }

        .main_graph_table_container::-webkit-scrollbar-track {
            background: #f0f0f0;
            border-radius: 10px;
        }

        .main_graph_table_container::-webkit-scrollbar-thumb {
            background: #2989d8;
            border-radius: 10px;
        }

        .main_graph_table_container::-webkit-scrollbar-thumb:hover {
            background: #1e5799;
        }

        /* 表格视图容器 */
        .table-custom-container {
            position: relative;
            width: 100%;
            padding: 20px;
            margin: 20px auto;
            max-width: 1140px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table-custom-container.hidden {
            display: none !important;
        }

        /* 视图切换按钮 */
        .view-toggle {
            position: fixed;
            left: 50%;
            bottom: 18px;
            transform: translateX(-50%);
            z-index: 2000;
            display: flex;
            gap: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 20px;
            border-radius: 50px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .toggle-btn {
            width: 120px;
            height: 50px;
            border-radius: 25px;
            background: linear-gradient(135deg, #1e5799, #2989d8);
            color: white;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 123, 255, 0.5);
        }

        .toggle-btn.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        /* 内容样式 */
        .content {
            padding: 20px;
            margin: 20px 0;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .long-content {
            height: 2000px;
            background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #1976d2;
        }
    </style>
</head>
<body class="graph-view-active">
    <!-- 图示视图容器 -->
    <div id="main_graph_table_container" class="main_graph_table_container graph-view-active">
        <h1 style="text-align: center; color: #1976d2;">图示视图</h1>
        <div class="content">
            <h2>这是图示视图的内容</h2>
            <p>这个视图应该只有一个滚动条（在右侧），用于滚动图示视图的内容。</p>
            <p>页面主体不应该有滚动条。</p>
        </div>
        
        <!-- 模拟长内容来测试滚动 -->
        <div class="long-content">
            <div>
                <h3>长内容区域</h3>
                <p>滚动测试内容...</p>
                <p>这里有很多内容需要滚动查看</p>
                <p>只应该有图示视图容器的滚动条</p>
                <p>页面主体应该没有滚动条</p>
            </div>
        </div>
    </div>

    <!-- 表格视图容器 -->
    <div id="table_container" class="table-custom-container hidden">
        <h1 style="text-align: center; color: #1976d2;">表格视图</h1>
        <div class="content">
            <h2>这是表格视图的内容</h2>
            <p>这个视图应该使用页面主体的滚动条。</p>
            <p>图示视图容器应该被隐藏。</p>
        </div>
        
        <!-- 模拟长内容来测试滚动 -->
        <div class="long-content">
            <div>
                <h3>表格内容区域</h3>
                <p>滚动测试内容...</p>
                <p>这里有很多内容需要滚动查看</p>
                <p>应该使用页面主体的滚动条</p>
                <p>图示视图容器应该被隐藏</p>
            </div>
        </div>
    </div>

    <!-- 视图切换按钮 -->
    <div class="view-toggle">
        <button id="toggle_graph_btn" class="toggle-btn active">图示视图</button>
        <button id="toggle_table_btn" class="toggle-btn">表格视图</button>
    </div>

    <script>
        // 视图切换功能
        function toggleView(viewType) {
            const tableContainer = document.getElementById('table_container');
            const graphContainer = document.getElementById('main_graph_table_container');
            const toggleGraphBtn = document.getElementById('toggle_graph_btn');
            const toggleTableBtn = document.getElementById('toggle_table_btn');

            if (viewType === 'table') {
                // 切换到表格视图
                if (graphContainer) {
                    graphContainer.classList.add('hidden');
                    graphContainer.classList.remove('graph-view-active');
                }
                if (tableContainer) {
                    tableContainer.classList.remove('hidden');
                }
                
                // 更新按钮状态
                toggleTableBtn.classList.add('active');
                toggleGraphBtn.classList.remove('active');

                // 恢复页面主体滚动条，隐藏图示视图滚动条
                document.body.classList.remove('graph-view-active');
                document.body.classList.add('table-view-active');
            } else {
                // 切换到图示视图
                if (graphContainer) {
                    graphContainer.classList.remove('hidden');
                    graphContainer.classList.add('graph-view-active');
                }
                if (tableContainer) {
                    tableContainer.classList.add('hidden');
                }
                
                // 更新按钮状态
                toggleGraphBtn.classList.add('active');
                toggleTableBtn.classList.remove('active');

                // 隐藏页面主体滚动条，只保留图示视图内部滚动条
                document.body.classList.remove('table-view-active');
                document.body.classList.add('graph-view-active');
            }
        }

        // 绑定事件
        document.getElementById('toggle_graph_btn').addEventListener('click', () => toggleView('graph'));
        document.getElementById('toggle_table_btn').addEventListener('click', () => toggleView('table'));
    </script>
</body>
</html>
